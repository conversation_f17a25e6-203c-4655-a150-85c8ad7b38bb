{"__meta": {"id": "01K21YXAHMN6JYJG7WS40E5K39", "datetime": "2025-08-07 10:48:33", "utime": **********.204695, "method": "GET", "uri": "/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 1, "messages": [{"message": "[10:48:33] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.17594, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.610333, "end": **********.213745, "duration": 0.6034121513366699, "duration_str": "603ms", "measures": [{"label": "Booting", "start": **********.610333, "relative_start": 0, "end": **********.810824, "relative_end": **********.810824, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.810835, "relative_start": 0.****************, "end": **********.213747, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.822221, "relative_start": 0.*****************, "end": **********.825519, "relative_end": **********.825519, "duration": 0.003298044204711914, "duration_str": "3.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.203306, "relative_start": 0.***************, "end": **********.203411, "relative_end": **********.203411, "duration": 0.00010514259338378906, "duration_str": "105μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.203426, "relative_start": 0.****************, "end": **********.203438, "relative_end": **********.203438, "duration": 1.2159347534179688e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "80MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "604ms", "peak_memory": "82MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1463160963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463160963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1006485243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1006485243\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-876361687 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IjZBOFFxTENzL24zRU9KOFhuVy9ya2c9PSIsInZhbHVlIjoiWHh1dUcvQ2hmZDcwSnVScStrekJtU05naENuY1VhaWRac0o1R1FXZHlEZE1TY0VwTU10L0dEVHM3V1k3RkpzK1R1K2NFeElmc0x0NnN5WEFwSU1nVHhvMElnc3NKZjhWVFc4eVlmSi9rRUo3T3V6d0k4cXZtK256VnpqVmJncGwiLCJtYWMiOiJmZTkyNzAxZjA2NTZlZjQ3ZWM3Zjg1MzFlNGUyMDRkNzYzM2IzY2VhY2MwNzk1ODJkMDZiY2Y1MDlhYzgzOTI1IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InNPbUpwejFlaThFUUt0eDZuTWR5aVE9PSIsInZhbHVlIjoieGdyTHd3UXRnaUZnWUpBYXo2U0tIaEJQY0tPeXRocERuSUk2WjBOSEFHTk1oUExLUHJLSmJOMWJNUWVyNURKSVJBcDJTeUNPaGZkSGF2dkYveCtxdmg4azZHTitUcGFtcU5QLzFtVVBkT25sMDhaOHJvT1ZWWE5QQ3ozVGRBM0QiLCJtYWMiOiJhMjRjMmUwODE3YTUzYjA2NGE0MTc3ZTljZTQ3NTY3NWMyYzQyMTI5NjQzMzFlMzA3MWNmNDJmZjg5ZGVlMzQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876361687\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-494794917 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZBOFFxTENzL24zRU9KOFhuVy9ya2c9PSIsInZhbHVlIjoiWHh1dUcvQ2hmZDcwSnVScStrekJtU05naENuY1VhaWRac0o1R1FXZHlEZE1TY0VwTU10L0dEVHM3V1k3RkpzK1R1K2NFeElmc0x0NnN5WEFwSU1nVHhvMElnc3NKZjhWVFc4eVlmSi9rRUo3T3V6d0k4cXZtK256VnpqVmJncGwiLCJtYWMiOiJmZTkyNzAxZjA2NTZlZjQ3ZWM3Zjg1MzFlNGUyMDRkNzYzM2IzY2VhY2MwNzk1ODJkMDZiY2Y1MDlhYzgzOTI1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InNPbUpwejFlaThFUUt0eDZuTWR5aVE9PSIsInZhbHVlIjoieGdyTHd3UXRnaUZnWUpBYXo2U0tIaEJQY0tPeXRocERuSUk2WjBOSEFHTk1oUExLUHJLSmJOMWJNUWVyNURKSVJBcDJTeUNPaGZkSGF2dkYveCtxdmg4azZHTitUcGFtcU5QLzFtVVBkT25sMDhaOHJvT1ZWWE5QQ3ozVGRBM0QiLCJtYWMiOiJhMjRjMmUwODE3YTUzYjA2NGE0MTc3ZTljZTQ3NTY3NWMyYzQyMTI5NjQzMzFlMzA3MWNmNDJmZjg5ZGVlMzQyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494794917\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1296114250 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">38380</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">7830b97cd20453e4d5dfdeda5d484a5f</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:48:33 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296114250\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-902529013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-902529013\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}