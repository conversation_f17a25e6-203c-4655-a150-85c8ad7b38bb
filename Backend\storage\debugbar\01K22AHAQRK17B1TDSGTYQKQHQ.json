{"__meta": {"id": "01K22AHAQRK17B1TDSGTYQKQHQ", "datetime": "2025-08-07 14:11:43", "utime": **********.096746, "method": "PUT", "uri": "/admin/catalog/categories/edit/10", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (1)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (8)", "Webkul\\Category\\Models\\CategoryTranslation (40)"], "views": [], "queries": [{"sql": "select exists(select 1 from `category_translations` where `category_id` <> '10' and `slug` = 'power-and-cables' limit 1) as `exists`", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 10 and `categories`.`parent_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (17, 18, 19, 20, 21, 22)", "duration": 0.35, "duration_str": "350ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 17", "duration": 2.4, "duration_str": "2.4s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 17 and `categories`.`parent_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 18", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 18 and `categories`.`parent_id` is not null", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 19", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 19 and `categories`.`parent_id` is not null", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 20", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 20 and `categories`.`parent_id` is not null", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 21", "duration": 2.17, "duration_str": "2.17s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 21 and `categories`.`parent_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 22", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`parent_id` = 22 and `categories`.`parent_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `categories` set `logo_path` = 'category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp', `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 10", "duration": 2.32, "duration_str": "2.32s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 17", "duration": 2.17, "duration_str": "2.17s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 18", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 19", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 20", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 21", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 22", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": [], "views": [], "queries": [{"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'power-and-cables'", "duration": 0.56, "duration_str": "560ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.705261, "end": **********.103386, "duration": 0.39812493324279785, "duration_str": "398ms", "measures": [{"label": "Booting", "start": **********.705261, "relative_start": 0, "end": **********.873879, "relative_end": **********.873879, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.87389, "relative_start": 0.*****************, "end": **********.103387, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.883908, "relative_start": 0.*****************, "end": **********.885284, "relative_end": **********.885284, "duration": 0.0013759136199951172, "duration_str": "1.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.09476, "relative_start": 0.****************, "end": **********.095044, "relative_end": **********.095044, "duration": 0.00028395652770996094, "duration_str": "284μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03368, "accumulated_duration_str": "33.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.895955, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 1.544}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.899502, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 1.544, "width_percent": 0.683}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.904633, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 2.227, "width_percent": 0.683}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.906384, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 2.91, "width_percent": 0.802}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.907547, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 3.711, "width_percent": 0.475}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.910464, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 4.186, "width_percent": 0.594}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.9124131, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 4.78, "width_percent": 0.831}, {"sql": "select exists(select 1 from `category_translations` where `category_id` <> '10' and `slug` = 'power-and-cables' limit 1) as `exists`", "type": "query", "params": [], "bindings": ["10", "power-and-cables"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 97}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 77}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}], "start": **********.915859, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ProductCategoryUniqueSlug.php:97", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FProductCategoryUniqueSlug.php&line=97", "ajax": false, "filename": "ProductCategoryUniqueSlug.php", "line": "97"}, "connection": "mlk", "explain": null, "start_percent": 5.612, "width_percent": 0.683}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 125}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}], "start": **********.921121, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 6.295, "width_percent": 0.624}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'power-and-cables'", "type": "query", "params": [], "bindings": [3, "power-and-cables"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.9222429, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 6.918, "width_percent": 1.663}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 154}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.924301, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 8.581, "width_percent": 0.564}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Category.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Category.php", "line": 64}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 115}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9297621, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 9.145, "width_percent": 0.831}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Category.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Category.php", "line": 64}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 115}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.932101, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 9.976, "width_percent": 0.653}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 113}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.933152, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 10.629, "width_percent": 0.475}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 25, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 113}, {"index": 26, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.934079, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 11.105, "width_percent": 0.594}, {"sql": "select * from `categories` where `categories`.`parent_id` = 10 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9446082, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 11.698, "width_percent": 0.802}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 26, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 34, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 35, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.945699, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 12.5, "width_percent": 1.039}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 17", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 17], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.946995, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 13.539, "width_percent": 7.126}, {"sql": "select * from `categories` where `categories`.`parent_id` = 17 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.9502609, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 20.665, "width_percent": 0.594}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 18", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 18], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.952086, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 21.259, "width_percent": 6.265}, {"sql": "select * from `categories` where `categories`.`parent_id` = 18 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.955125, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 27.524, "width_percent": 0.475}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 19", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9558551, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 27.999, "width_percent": 6.146}, {"sql": "select * from `categories` where `categories`.`parent_id` = 19 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.958865, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 34.145, "width_percent": 0.475}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.959574, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 34.62, "width_percent": 6.116}, {"sql": "select * from `categories` where `categories`.`parent_id` = 20 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.96254, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 40.736, "width_percent": 0.713}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 21", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 21], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.963563, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 41.449, "width_percent": 6.443}, {"sql": "select * from `categories` where `categories`.`parent_id` = 21 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.966676, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 47.892, "width_percent": 0.594}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:42' where `id` = 22", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:42", 22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.967467, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 48.486, "width_percent": 6.116}, {"sql": "select * from `categories` where `categories`.`parent_id` = 22 and `categories`.`parent_id` is not null", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 29}, {"index": 29, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 37, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 117}, {"index": 38, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}], "start": **********.970432, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 54.602, "width_percent": 0.534}, {"sql": "update `categories` set `logo_path` = 'category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp', `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 10", "type": "query", "params": [], "bindings": ["category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp", "2025-08-07 14:11:43", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0542998, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:256", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FRepositories%2FCategoryRepository.php&line=256", "ajax": false, "filename": "CategoryRepository.php", "line": "256"}, "connection": "mlk", "explain": null, "start_percent": 55.137, "width_percent": 6.888}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 17", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 17], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.059392, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 62.025, "width_percent": 6.443}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 18", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 18], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0628111, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 68.468, "width_percent": 6.027}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 19", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 19], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.065842, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 74.495, "width_percent": 6.205}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 20", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0690372, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 80.701, "width_percent": 6.027}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 21", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 21], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.072042, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 86.728, "width_percent": 6.027}, {"sql": "update `categories` set `categories`.`updated_at` = '2025-08-07 14:11:43' where `id` = 22", "type": "query", "params": [], "bindings": ["2025-08-07 14:11:43", 22], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 256}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 119}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0750341, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "CategoryObserver.php:30", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Observers/CategoryObserver.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Observers\\CategoryObserver.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FObservers%2FCategoryObserver.php&line=30", "ajax": false, "filename": "CategoryObserver.php", "line": "30"}, "connection": "mlk", "explain": null, "start_percent": 92.755, "width_percent": 5.998}, {"sql": "select * from `category_filterable_attributes` where `category_filterable_attributes`.`category_id` = 10", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 124}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\CategoryController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.081148, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:124", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FRepositories%2FCategoryRepository.php&line=124", "ajax": false, "filename": "CategoryRepository.php", "line": "124"}, "connection": "mlk", "explain": null, "start_percent": 98.753, "width_percent": 0.683}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.087676, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 99.436, "width_percent": 0.564}]}, "models": {"data": {"Webkul\\Category\\Models\\CategoryTranslation": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "Webkul\\Category\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 54, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/categories/edit/10", "action_name": "admin.catalog.categories.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\CategoryController@update", "uri": "PUT admin/catalog/categories/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\CategoryController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FCategoryController.php&line=113\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/categories", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FCategoryController.php&line=113\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/CategoryController.php:113-134</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "401ms", "peak_memory": "46MB", "response": "Redirect to http://mlk.test/admin/catalog/categories", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-563944347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-563944347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1384807172 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">POWER AND CABLES</span>\"\n    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&lt;p&gt;POWER&amp;nbsp;&lt;/p&gt;</span>\"\n    \"<span class=sf-dump-key>meta_title</span>\" => \"\"\n    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"16 characters\">power-and-cables</span>\"\n    \"<span class=sf-dump-key>meta_keywords</span>\" => \"\"\n    \"<span class=sf-dump-key>meta_description</span>\" => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>parent_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>display_mode</span>\" => \"<span class=sf-dump-str title=\"24 characters\">products_and_description</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">24</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384807172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1847324859 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IndBZnpKeWsyMzhJVXJ5SnJQM05JblE9PSIsInZhbHVlIjoiK2dDbUF0QStXYTBONG5yR0ZSanRFNENxYU05cUh1bHRHVnBUV2JlQUFqK1dZbEtWdU92SVBDb1pxRHdyT09wMmVEZFhPZlpBdlJBMndEZnY3Nmt0S2xTRWNVemdNVmRUdkE5ZkgrcDZ6cFdXN2VnTmk0akJEcTZTNWQzOW15b3giLCJtYWMiOiJlNTRiNzU5YmVmM2JlZWFkNTkxMzg0ODJmNDlhMTk3MmU3MDZhMzNmYmI5YzU3MGIwYjFhNDg5NDFjMmIwYjM2IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkpBU3VnMVN1bHFXQ1Q2Wm00NVFOdkE9PSIsInZhbHVlIjoiLytjQU40ditKWFAvNHpUNW8yV0wzaHdLTkpqaVVqU1dMSi9IYXlOb0wvbDRDVkdVTU9teUFCNExPalZHWHRqZEtSWmF2MjljMnFlcmpIWExsc2paRncxLzcxWHFQZXF6S1dSc2RUVlUwSmNCbExidTlBbDBjYmtJSlZ4bnZVZGoiLCJtYWMiOiIxMzVlYmYwNDNkMGIyYjk3MmMwZjgyMzRiMzJmMjg3ZTY1YzFhOTU3MjBiNDUzMTc2OGZkMjIyNDkyODUwNGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"74 characters\">http://mlk.test/admin/catalog/categories/edit/10?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarypMbUQBe69GvLlBAa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">153568</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847324859\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1167597216 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167597216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2076419899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 13:11:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://mlk.test/admin/catalog/categories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076419899\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-444121353 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"74 characters\">http://mlk.test/admin/catalog/categories/edit/10?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#20998;&#31867;&#26356;&#26032;&#25104;&#21151;&#12290;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444121353\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/categories/edit/10", "action_name": "admin.catalog.categories.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\CategoryController@update"}, "badge": "302 Found"}}