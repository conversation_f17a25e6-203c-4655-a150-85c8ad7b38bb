{"__meta": {"id": "01K21YK3ND6W7TDCNBQ2MP3HFN", "datetime": "2025-08-07 10:42:58", "utime": **********.477988, "method": "GET", "uri": "/cache/original/product/181/ebkefNhh6T5WI1SrhR60M3oiB00YbhS69mxtKSV5.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.284571, "end": **********.487571, "duration": 0.20300006866455078, "duration_str": "203ms", "measures": [{"label": "Booting", "start": **********.284571, "relative_start": 0, "end": **********.459711, "relative_end": **********.459711, "duration": 0.****************, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.459721, "relative_start": 0.*****************, "end": **********.487574, "relative_end": 3.0994415283203125e-06, "duration": 0.027853012084960938, "duration_str": "27.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.470219, "relative_start": 0.*****************, "end": **********.473569, "relative_end": **********.473569, "duration": 0.0033500194549560547, "duration_str": "3.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.476651, "relative_start": 0.*****************, "end": **********.476757, "relative_end": **********.476757, "duration": 0.00010609626770019531, "duration_str": "106μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.476768, "relative_start": 0.*****************, "end": **********.47678, "relative_end": **********.47678, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/181/ebkefNhh6T5WI1SrhR60M3oiB00YbhS69mxtKSV5.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "203ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-230002641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-230002641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-322171606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-322171606\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1939317589 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlhuZ09mdmpqVHlHZmpjNngvUWExaXc9PSIsInZhbHVlIjoiYWV0UTZQRGFSQ2d5SWY1cy93NkFTT0ZQb0ZRSzRWZS9DdkNDWFBUK0VPb2ZaRUE1SEpwZlVoMnZDN25PbGJTYUkvQ0xxTElLZVI4SDNNdkViSkpTQ0ZhR0Fma2xFYzl3MmNncERPZ3krc0gvVFRRb00zblRwcXJwOVA5a25la3IiLCJtYWMiOiI4YWU0MDUxM2I2Nzk1MmM0ZDkwZDVlYThkN2JlN2QwOWQ1YzhhODZkMWRhNzRiZmMzOTM4Nzc3YjEyMzE3MWJlIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ikt0RlRuZFM0MC9KdFgySmpmVmFCc3c9PSIsInZhbHVlIjoiNDRTVWFwZmtOYkd6WXNDc08xc2IrYUs5d043d3VkbHkwTTdLeDBaSm94ZWdTVVZSVVJOMGxhUWNqQ2Z2K2FNZmlDSnZ5a1NGKzRkYkc0YmZGYmdqR3pwZWtSTkhFcms2cHZVenNGVTdxRDJmRE9RQVo2RUVFdkxxeDdRK2ZBSUQiLCJtYWMiOiI3YjBhMzcwNWQ1NzJmNzM1YTNkOTg0M2Y3ZWQ0NDQ1NmQ5NWRmODg3Yzk1MGQ4NDAxMjQ1ODkxOTlmMzBmMzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://mlk.test/case-for-1031-easy-access-to-all-buttons</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939317589\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1076235688 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlhuZ09mdmpqVHlHZmpjNngvUWExaXc9PSIsInZhbHVlIjoiYWV0UTZQRGFSQ2d5SWY1cy93NkFTT0ZQb0ZRSzRWZS9DdkNDWFBUK0VPb2ZaRUE1SEpwZlVoMnZDN25PbGJTYUkvQ0xxTElLZVI4SDNNdkViSkpTQ0ZhR0Fma2xFYzl3MmNncERPZ3krc0gvVFRRb00zblRwcXJwOVA5a25la3IiLCJtYWMiOiI4YWU0MDUxM2I2Nzk1MmM0ZDkwZDVlYThkN2JlN2QwOWQ1YzhhODZkMWRhNzRiZmMzOTM4Nzc3YjEyMzE3MWJlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikt0RlRuZFM0MC9KdFgySmpmVmFCc3c9PSIsInZhbHVlIjoiNDRTVWFwZmtOYkd6WXNDc08xc2IrYUs5d043d3VkbHkwTTdLeDBaSm94ZWdTVVZSVVJOMGxhUWNqQ2Z2K2FNZmlDSnZ5a1NGKzRkYkc0YmZGYmdqR3pwZWtSTkhFcms2cHZVenNGVTdxRDJmRE9RQVo2RUVFdkxxeDdRK2ZBSUQiLCJtYWMiOiI3YjBhMzcwNWQ1NzJmNzM1YTNkOTg0M2Y3ZWQ0NDQ1NmQ5NWRmODg3Yzk1MGQ4NDAxMjQ1ODkxOTlmMzBmMzgxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076235688\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1183732135 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">32732</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">a4717e300cc331221e231ba8dbfdfa56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:42:58 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183732135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-143842900 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-143842900\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/181/ebkefNhh6T5WI1SrhR60M3oiB00YbhS69mxtKSV5.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}