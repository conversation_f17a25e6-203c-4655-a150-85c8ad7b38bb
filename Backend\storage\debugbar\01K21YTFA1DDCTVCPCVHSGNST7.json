{"__meta": {"id": "01K21YTFA1DDCTVCPCVHSGNST7", "datetime": "2025-08-07 10:46:59", "utime": **********.777804, "method": "GET", "uri": "/cache/small/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.546968, "end": **********.790792, "duration": 0.24382400512695312, "duration_str": "244ms", "measures": [{"label": "Booting", "start": **********.546968, "relative_start": 0, "end": **********.749179, "relative_end": **********.749179, "duration": 0.****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.749193, "relative_start": 0.****************, "end": **********.790794, "relative_end": 1.9073486328125e-06, "duration": 0.041600942611694336, "duration_str": "41.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.763631, "relative_start": 0.*****************, "end": **********.768937, "relative_end": **********.768937, "duration": 0.0053060054779052734, "duration_str": "5.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.775919, "relative_start": 0.*****************, "end": **********.776056, "relative_end": **********.776056, "duration": 0.00013709068298339844, "duration_str": "137μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.776072, "relative_start": 0.*****************, "end": **********.776087, "relative_end": **********.776087, "duration": 1.5020370483398438e-05, "duration_str": "15μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "244ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-226786979 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-226786979\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1538904308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1538904308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1218786158 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Im1WcFVsWjllWTNhQlE2RkFvSHhMV2c9PSIsInZhbHVlIjoid2FkT3RXTDNRSldzZFNRUlFyNWtKTFV5WXBvKzBNcmUrc0RXM29UZ2hqZ0FnbzM4YUZWZERMVG1VVld0MEY3bWo4SERhODB0NEJEcE1xY1hQKytUY3BnYm5SZytGeGhmOWZ6U3VRNVg4cWN6U0N3ek5uMVlWVy9zd3NMT3ovbnciLCJtYWMiOiI1NzY0Y2E1ZTA3YTRiYzc3NmQ2MGE1Mjg2ZGQyYzNmOWVlMjRjZmY1MDY2ODQ4ZjU0YjcwMmY1OTFlMjhlMjllIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlpuVE9NejVuZ3orUVNXWUtDT2J3bGc9PSIsInZhbHVlIjoiWWRBUzJlRE4ya0w4a3ZrbE9vMVpQRTBTcDA5ZG52ei9tYzJSaUpGbGI1ajdTVTRUVTB1M3UyVVp2d04xeTNIK2FOdkdMTktMY1prMGh1Z2laM2t1aDY5T2tjSEh1dGtDMm00b3lOSkwwLy8yc0Z4U1FHVHNPcWxjQi9RM3JCdGEiLCJtYWMiOiJlODJlYjllZTYxOTljYzkxYTI3ZDBjMWZjODgwZGI2YjQ4ZTljZTAwZDI1NDIzNmEyYzRiMmZkYWE2ZjkyYWUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://mlk.test/SKU-CS-011-variant-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218786158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1173445445 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1WcFVsWjllWTNhQlE2RkFvSHhMV2c9PSIsInZhbHVlIjoid2FkT3RXTDNRSldzZFNRUlFyNWtKTFV5WXBvKzBNcmUrc0RXM29UZ2hqZ0FnbzM4YUZWZERMVG1VVld0MEY3bWo4SERhODB0NEJEcE1xY1hQKytUY3BnYm5SZytGeGhmOWZ6U3VRNVg4cWN6U0N3ek5uMVlWVy9zd3NMT3ovbnciLCJtYWMiOiI1NzY0Y2E1ZTA3YTRiYzc3NmQ2MGE1Mjg2ZGQyYzNmOWVlMjRjZmY1MDY2ODQ4ZjU0YjcwMmY1OTFlMjhlMjllIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlpuVE9NejVuZ3orUVNXWUtDT2J3bGc9PSIsInZhbHVlIjoiWWRBUzJlRE4ya0w4a3ZrbE9vMVpQRTBTcDA5ZG52ei9tYzJSaUpGbGI1ajdTVTRUVTB1M3UyVVp2d04xeTNIK2FOdkdMTktMY1prMGh1Z2laM2t1aDY5T2tjSEh1dGtDMm00b3lOSkwwLy8yc0Z4U1FHVHNPcWxjQi9RM3JCdGEiLCJtYWMiOiJlODJlYjllZTYxOTljYzkxYTI3ZDBjMWZjODgwZGI2YjQ4ZTljZTAwZDI1NDIzNmEyYzRiMmZkYWE2ZjkyYWUwIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173445445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35889625 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1156</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">c26144837ccf5314cab984a144952cc2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:46:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35889625\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-856035575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-856035575\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/small/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}