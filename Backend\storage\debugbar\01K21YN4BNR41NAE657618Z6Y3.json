{"__meta": {"id": "01K21YN4BNR41NAE657618Z6Y3", "datetime": "2025-08-07 10:44:04", "utime": **********.725598, "method": "GET", "uri": "/cache/medium/product/185/Rszv79g6k9Z2TchuaSSDs5cZCwL9HuqWsAmT2N9H.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.549305, "end": **********.734124, "duration": 0.18481898307800293, "duration_str": "185ms", "measures": [{"label": "Booting", "start": **********.549305, "relative_start": 0, "end": **********.705741, "relative_end": **********.705741, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.70575, "relative_start": 0.*****************, "end": **********.734125, "relative_end": 9.5367431640625e-07, "duration": 0.028374910354614258, "duration_str": "28.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.715008, "relative_start": 0.*****************, "end": **********.718195, "relative_end": **********.718195, "duration": 0.003186941146850586, "duration_str": "3.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.724345, "relative_start": 0.*****************, "end": **********.724436, "relative_end": **********.724436, "duration": 9.107589721679688e-05, "duration_str": "91μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.724446, "relative_start": 0.****************, "end": **********.724456, "relative_end": **********.724456, "duration": 1.0013580322265625e-05, "duration_str": "10μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/185/Rszv79g6k9Z2TchuaSSDs5cZCwL9HuqWsAmT2N9H.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "185ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-266649403 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-266649403\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-92773297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-92773297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1744996274 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkxZWG8rZWVEVC9uMnFTbzhUWjFwM0E9PSIsInZhbHVlIjoiVG9qWEI4NHVpaFN6NjBTeXZzQXQ1THR3eW9ybkxOek9tOGRKZ0ZsVTlmRXlMTnVreEZkaEtKMks2d1lzM1pFcjJyZ3RoQTV3aWJlN0NoWjRGR0p6bjN6SCtIV0k1bEdhK1UzLy84YUxLNm5iWUJpaG45QzlzcXVTUTlXV2VlbVkiLCJtYWMiOiIzOGQyMmU0YjExM2EyMGY1NWNiMTk2OGRkOTNiMTQ1ZTMwN2YwNmRhODRmMDFkOTVkNjg4MzU1NmM0Mjc2OTZkIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ink3bkU4VXpMRE1VZEhmQ1dKblZBU1E9PSIsInZhbHVlIjoidkNZcUx6S3RvemllQ1hhcjVaMFUwcUZBOFFyNW1DcEdMZncxSjhjVWt1K1dBT0JseXBTdEVVYlJIM3pXdWJvWXl2dHRhOGhGWktiUWY1OXpXQ1hQMm5TSlZDSGp0ME5tMURid0ttamNkS0R6NVdBY2tyLzZMR1kwQ0tMMHBtTlciLCJtYWMiOiI5NmRjZjBkZTRkYmEzN2NkMGVlMDVlMzg1YjgxZDU5YTI5YTgxOWQwZGE5Yzk2NzgwNjRhOGQ3ZWMzOGVhZGE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744996274\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-643285935 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxZWG8rZWVEVC9uMnFTbzhUWjFwM0E9PSIsInZhbHVlIjoiVG9qWEI4NHVpaFN6NjBTeXZzQXQ1THR3eW9ybkxOek9tOGRKZ0ZsVTlmRXlMTnVreEZkaEtKMks2d1lzM1pFcjJyZ3RoQTV3aWJlN0NoWjRGR0p6bjN6SCtIV0k1bEdhK1UzLy84YUxLNm5iWUJpaG45QzlzcXVTUTlXV2VlbVkiLCJtYWMiOiIzOGQyMmU0YjExM2EyMGY1NWNiMTk2OGRkOTNiMTQ1ZTMwN2YwNmRhODRmMDFkOTVkNjg4MzU1NmM0Mjc2OTZkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ink3bkU4VXpMRE1VZEhmQ1dKblZBU1E9PSIsInZhbHVlIjoidkNZcUx6S3RvemllQ1hhcjVaMFUwcUZBOFFyNW1DcEdMZncxSjhjVWt1K1dBT0JseXBTdEVVYlJIM3pXdWJvWXl2dHRhOGhGWktiUWY1OXpXQ1hQMm5TSlZDSGp0ME5tMURid0ttamNkS0R6NVdBY2tyLzZMR1kwQ0tMMHBtTlciLCJtYWMiOiI5NmRjZjBkZTRkYmEzN2NkMGVlMDVlMzg1YjgxZDU5YTI5YTgxOWQwZGE5Yzk2NzgwNjRhOGQ3ZWMzOGVhZGE1IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643285935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2065172930 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">9326</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">3bf462cb7c1a9d90a0865a0555f3c5c6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:44:04 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065172930\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-117174322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-117174322\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/185/Rszv79g6k9Z2TchuaSSDs5cZCwL9HuqWsAmT2N9H.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}