{"__meta": {"id": "01K22AHWYR5DE704TFZ48KF8YZ", "datetime": "2025-08-07 14:12:01", "utime": **********.75261, "method": "GET", "uri": "/cache/large/category/4/65jfFcxVyTkK0vb4xGDDYqEe5WCMyhqcGs9P1a3A.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 1, "messages": [{"message": "[14:12:01] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.744094, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.516905, "end": **********.763321, "duration": 0.2464158535003662, "duration_str": "246ms", "measures": [{"label": "Booting", "start": **********.516905, "relative_start": 0, "end": **********.689348, "relative_end": **********.689348, "duration": 0.*****************, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.689358, "relative_start": 0.****************, "end": **********.763323, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "73.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.700126, "relative_start": 0.*****************, "end": **********.706164, "relative_end": **********.706164, "duration": 0.*****************, "duration_str": "6.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.750811, "relative_start": 0.*****************, "end": **********.750915, "relative_end": **********.750915, "duration": 0.00010395050048828125, "duration_str": "104μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.75093, "relative_start": 0.****************, "end": **********.750943, "relative_end": **********.750943, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/category/4/65jfFcxVyTkK0vb4xGDDYqEe5WCMyhqcGs9P1a3A.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "247ms", "peak_memory": "38MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-828760636 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-828760636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-67965235 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-67965235\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-150046469 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Ik1BSTU5NnMxc25BcU91MTJjMzJRR0E9PSIsInZhbHVlIjoiNU51dnMxdWVPUXUxalJWTjZRcTZXM1N4a203elBZYjFweEFNMS95WGE1STRtcytSTHl2dlVrMXNsUm0vNCtrR0w5aGVUZ2dWRWVPQ3RaUDJ2VzFHaXF3bHI0TXd6MjdFb1BsTXNxSC82bUxGMnZPQU5sYVdqK1pSbWdBRThrMVciLCJtYWMiOiJmYjk3Y2NjYWEyZjZjY2E2OTc4NGQxMDdkYmE5Mzk3OWM4MmNkZThmNzkxNWU5ZmRkMzBhOTZkMzZiODhlYWUzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ikk4NEZqbjNPRkh1QWMxeS9CUy9ZWEE9PSIsInZhbHVlIjoiSVQyaFZialgyODc1bU9pUHlxZy84WWt6anIrUVZQaVIwUVV6SCt1dXpiK2ZlaDhac3JONHpPWG9zQU93ZE1nQTdEeVhGQitLbThIMkhxSTZIR21lRi9WQmlvQmJ1YTlkSlhEaE9wTFZzN2xGUjZRdzRoeGJkQWtiUURwcjRtQ3QiLCJtYWMiOiIwYWZiMzM1NWYxMmNlNGMxYjRmMTNiMzdjMTM4NGQ1ODk5NWI5YzA3NjEyMGVjMWRiMjE0NzllNTVmNDZjZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150046469\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-718994205 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1BSTU5NnMxc25BcU91MTJjMzJRR0E9PSIsInZhbHVlIjoiNU51dnMxdWVPUXUxalJWTjZRcTZXM1N4a203elBZYjFweEFNMS95WGE1STRtcytSTHl2dlVrMXNsUm0vNCtrR0w5aGVUZ2dWRWVPQ3RaUDJ2VzFHaXF3bHI0TXd6MjdFb1BsTXNxSC82bUxGMnZPQU5sYVdqK1pSbWdBRThrMVciLCJtYWMiOiJmYjk3Y2NjYWEyZjZjY2E2OTc4NGQxMDdkYmE5Mzk3OWM4MmNkZThmNzkxNWU5ZmRkMzBhOTZkMzZiODhlYWUzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikk4NEZqbjNPRkh1QWMxeS9CUy9ZWEE9PSIsInZhbHVlIjoiSVQyaFZialgyODc1bU9pUHlxZy84WWt6anIrUVZQaVIwUVV6SCt1dXpiK2ZlaDhac3JONHpPWG9zQU93ZE1nQTdEeVhGQitLbThIMkhxSTZIR21lRi9WQmlvQmJ1YTlkSlhEaE9wTFZzN2xGUjZRdzRoeGJkQWtiUURwcjRtQ3QiLCJtYWMiOiIwYWZiMzM1NWYxMmNlNGMxYjRmMTNiMzdjMTM4NGQ1ODk5NWI5YzA3NjEyMGVjMWRiMjE0NzllNTVmNDZjZjMxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718994205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-882680897 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4342</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">120643fe39963913fa470abf67d4ced8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 13:12:01 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882680897\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2055289106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2055289106\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/category/4/65jfFcxVyTkK0vb4xGDDYqEe5WCMyhqcGs9P1a3A.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}