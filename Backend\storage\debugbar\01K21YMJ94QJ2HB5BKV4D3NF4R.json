{"__meta": {"id": "01K21YMJ94QJ2HB5BKV4D3NF4R", "datetime": "2025-08-07 10:43:46", "utime": **********.21315, "method": "GET", "uri": "/cache/medium/product/174/8r8TIgYObmz8bi2PiimK3bXwqwwGHeNJbzG9AQLI.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.000094, "end": **********.222881, "duration": 0.22278714179992676, "duration_str": "223ms", "measures": [{"label": "Booting", "start": **********.000094, "relative_start": 0, "end": **********.192836, "relative_end": **********.192836, "duration": 0.*****************, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.192846, "relative_start": 0.*****************, "end": **********.222884, "relative_end": 2.86102294921875e-06, "duration": 0.030037879943847656, "duration_str": "30.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.203336, "relative_start": 0.*****************, "end": **********.206788, "relative_end": **********.206788, "duration": 0.0034520626068115234, "duration_str": "3.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.211731, "relative_start": 0.*****************, "end": **********.21183, "relative_end": **********.21183, "duration": 9.894371032714844e-05, "duration_str": "99μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.211844, "relative_start": 0.*****************, "end": **********.211856, "relative_end": **********.211856, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/174/8r8TIgYObmz8bi2PiimK3bXwqwwGHeNJbzG9AQLI.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "223ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1320509534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1320509534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2019238608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019238608\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-261955441 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Ik1OMCtsb2NvYkx5ZTJDYzRIcS9FM3c9PSIsInZhbHVlIjoicFJSZ0gxWkVqb3orRElDZi9ycU1DUGVJQXpsbEJ2Y2w3dXZsdmJydlhReXlidGllZy94bzZVWTJVUGNQWWEzRllybVltYXRJNjZ6V3pBSVJ6WWFYSWVzQmJJaVo5TW4vR2tkVmtGblJDZW5kaW5jeTNJSTAyRCt5U1JQQjBNRVkiLCJtYWMiOiJkOGRjYzAwZTUyZWRlNzNiYmUwMzg1MjNlODc3ZmUzOTc2ZjQ3OTdjNmY3ZjJhMTNjNWQzNTYzZTc1YzVkMzIzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImFkSFF6MXBld1M1enVqaEgyK0hFM3c9PSIsInZhbHVlIjoiM1VBOVZEL1V0OVA3Rnhtek1hOUNUU1U0d0hRWXV5SkVyQ1kxcmFqcWl1NDhXUndEWHZhVUxka3JnOFltTE8raVpmR0xabkxjL2RSbXlzcFp3bURhRVYyVDRCZE9nOW54WXExQUpzNS90MmRMUk5CeEJNS1k4Y2J1a3IxTnRXR0kiLCJtYWMiOiJkMTk4N2JkOTJkYzNiN2FiYTVlZDY0NjdmNGJmYWZhZTBlZjMxODExZmJjNjMxMTEzMTgwNzYzYjBjOTE1NjhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/custodie-per-smartphone?sort=price-desc&amp;limit=2&amp;mode=grid</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261955441\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1797242380 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1OMCtsb2NvYkx5ZTJDYzRIcS9FM3c9PSIsInZhbHVlIjoicFJSZ0gxWkVqb3orRElDZi9ycU1DUGVJQXpsbEJ2Y2w3dXZsdmJydlhReXlidGllZy94bzZVWTJVUGNQWWEzRllybVltYXRJNjZ6V3pBSVJ6WWFYSWVzQmJJaVo5TW4vR2tkVmtGblJDZW5kaW5jeTNJSTAyRCt5U1JQQjBNRVkiLCJtYWMiOiJkOGRjYzAwZTUyZWRlNzNiYmUwMzg1MjNlODc3ZmUzOTc2ZjQ3OTdjNmY3ZjJhMTNjNWQzNTYzZTc1YzVkMzIzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImFkSFF6MXBld1M1enVqaEgyK0hFM3c9PSIsInZhbHVlIjoiM1VBOVZEL1V0OVA3Rnhtek1hOUNUU1U0d0hRWXV5SkVyQ1kxcmFqcWl1NDhXUndEWHZhVUxka3JnOFltTE8raVpmR0xabkxjL2RSbXlzcFp3bURhRVYyVDRCZE9nOW54WXExQUpzNS90MmRMUk5CeEJNS1k4Y2J1a3IxTnRXR0kiLCJtYWMiOiJkMTk4N2JkOTJkYzNiN2FiYTVlZDY0NjdmNGJmYWZhZTBlZjMxODExZmJjNjMxMTEzMTgwNzYzYjBjOTE1NjhmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797242380\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1760783884 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4782</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">3eeb59bfa2a51ba1405544757f7cf820</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:43:46 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760783884\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-735754664 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-735754664\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/174/8r8TIgYObmz8bi2PiimK3bXwqwwGHeNJbzG9AQLI.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}