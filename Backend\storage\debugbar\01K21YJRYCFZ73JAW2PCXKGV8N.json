{"__meta": {"id": "01K21YJRYCFZ73JAW2PCXKGV8N", "datetime": "2025-08-07 10:42:47", "utime": **********.50101, "method": "GET", "uri": "/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.307213, "end": **********.510168, "duration": 0.20295500755310059, "duration_str": "203ms", "measures": [{"label": "Booting", "start": **********.307213, "relative_start": 0, "end": **********.478629, "relative_end": **********.478629, "duration": 0.****************, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.47864, "relative_start": 0.*****************, "end": **********.51017, "relative_end": 1.9073486328125e-06, "duration": 0.031529903411865234, "duration_str": "31.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.488632, "relative_start": 0.*****************, "end": **********.492128, "relative_end": **********.492128, "duration": 0.003495931625366211, "duration_str": "3.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.499434, "relative_start": 0.*****************, "end": **********.499567, "relative_end": **********.499567, "duration": 0.00013303756713867188, "duration_str": "133μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.499582, "relative_start": 0.****************, "end": **********.499596, "relative_end": **********.499596, "duration": 1.4066696166992188e-05, "duration_str": "14μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "203ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1222189977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1222189977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-778920080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-778920080\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6InZ0b1k5MEs4TXc0WmNiNkxHcEFSNWc9PSIsInZhbHVlIjoiVGdvUzNpNWs2QVhxWllHZWJ0anpIYTA5cy8vWitxd2VjNytYU2YzeTlKRmxhMjRBVkk2bjU5NElKeXpWVlZuVk5FZTNzY01QUkQ0NDR5VHpYUThIRm01dGJtaEpjNEQrY2VpZlZNQ0d4Yjdza2pSRE8rUGhpZllBMGx2eTBqdTMiLCJtYWMiOiJkM2ExM2YxNjVhM2FhZTUwZjgwZWVkNzE3Y2JmMTlhMDhiZTdlMGViYjRiNmI0ZWI5YjMzMTVjZTlkNTVlNDhiIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlZLcVNvTU1MMUhXU3FKTU8zSm5QSEE9PSIsInZhbHVlIjoiZXhtcnVyc0s3bzNzNU50T1ZYVUVWRG0xam9UcUo2VkM5NFRBaGZVUE8wcDFlOUQ2RW50UUdHV0N6UDMydXJTVkwybTRBRDZuZHoxdGNGM1htb01kWmdndGJCaW1zSytRb0lWaS9nVklVbTh0dStFTUlKcUFtRUVodFlmekxaN0giLCJtYWMiOiI4NWZlNTY5OWZmNGVkOWUzNDE3NjJjNDAzYTJjZWZhMjk4ZDVjMGIxZDU1ZTE2NTlkZDk1M2MxMmE4YjJkZjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-966301138 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZ0b1k5MEs4TXc0WmNiNkxHcEFSNWc9PSIsInZhbHVlIjoiVGdvUzNpNWs2QVhxWllHZWJ0anpIYTA5cy8vWitxd2VjNytYU2YzeTlKRmxhMjRBVkk2bjU5NElKeXpWVlZuVk5FZTNzY01QUkQ0NDR5VHpYUThIRm01dGJtaEpjNEQrY2VpZlZNQ0d4Yjdza2pSRE8rUGhpZllBMGx2eTBqdTMiLCJtYWMiOiJkM2ExM2YxNjVhM2FhZTUwZjgwZWVkNzE3Y2JmMTlhMDhiZTdlMGViYjRiNmI0ZWI5YjMzMTVjZTlkNTVlNDhiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlZLcVNvTU1MMUhXU3FKTU8zSm5QSEE9PSIsInZhbHVlIjoiZXhtcnVyc0s3bzNzNU50T1ZYVUVWRG0xam9UcUo2VkM5NFRBaGZVUE8wcDFlOUQ2RW50UUdHV0N6UDMydXJTVkwybTRBRDZuZHoxdGNGM1htb01kWmdndGJCaW1zSytRb0lWaS9nVklVbTh0dStFTUlKcUFtRUVodFlmekxaN0giLCJtYWMiOiI4NWZlNTY5OWZmNGVkOWUzNDE3NjJjNDAzYTJjZWZhMjk4ZDVjMGIxZDU1ZTE2NTlkZDk1M2MxMmE4YjJkZjc1IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966301138\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:42:47 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1402597458 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402597458\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}