{"__meta": {"id": "01K21YX79PB8AMXDKZC1BE72N0", "datetime": "2025-08-07 10:48:29", "utime": **********.87852, "method": "GET", "uri": "/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.689018, "end": **********.887571, "duration": 0.19855308532714844, "duration_str": "199ms", "measures": [{"label": "Booting", "start": **********.689018, "relative_start": 0, "end": **********.857531, "relative_end": **********.857531, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.857541, "relative_start": 0.*****************, "end": **********.887573, "relative_end": 1.9073486328125e-06, "duration": 0.030031919479370117, "duration_str": "30.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.86753, "relative_start": 0.****************, "end": **********.870901, "relative_end": **********.870901, "duration": 0.003371000289916992, "duration_str": "3.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.877106, "relative_start": 0.*****************, "end": **********.877207, "relative_end": **********.877207, "duration": 0.0001010894775390625, "duration_str": "101μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.877219, "relative_start": 0.****************, "end": **********.877229, "relative_end": **********.877229, "duration": 1.0013580322265625e-05, "duration_str": "10μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "199ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1820143930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1820143930\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1992822033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992822033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1700400417 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IjZBOFFxTENzL24zRU9KOFhuVy9ya2c9PSIsInZhbHVlIjoiWHh1dUcvQ2hmZDcwSnVScStrekJtU05naENuY1VhaWRac0o1R1FXZHlEZE1TY0VwTU10L0dEVHM3V1k3RkpzK1R1K2NFeElmc0x0NnN5WEFwSU1nVHhvMElnc3NKZjhWVFc4eVlmSi9rRUo3T3V6d0k4cXZtK256VnpqVmJncGwiLCJtYWMiOiJmZTkyNzAxZjA2NTZlZjQ3ZWM3Zjg1MzFlNGUyMDRkNzYzM2IzY2VhY2MwNzk1ODJkMDZiY2Y1MDlhYzgzOTI1IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InNPbUpwejFlaThFUUt0eDZuTWR5aVE9PSIsInZhbHVlIjoieGdyTHd3UXRnaUZnWUpBYXo2U0tIaEJQY0tPeXRocERuSUk2WjBOSEFHTk1oUExLUHJLSmJOMWJNUWVyNURKSVJBcDJTeUNPaGZkSGF2dkYveCtxdmg4azZHTitUcGFtcU5QLzFtVVBkT25sMDhaOHJvT1ZWWE5QQ3ozVGRBM0QiLCJtYWMiOiJhMjRjMmUwODE3YTUzYjA2NGE0MTc3ZTljZTQ3NTY3NWMyYzQyMTI5NjQzMzFlMzA3MWNmNDJmZjg5ZGVlMzQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"118 characters\">Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700400417\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-26545680 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZBOFFxTENzL24zRU9KOFhuVy9ya2c9PSIsInZhbHVlIjoiWHh1dUcvQ2hmZDcwSnVScStrekJtU05naENuY1VhaWRac0o1R1FXZHlEZE1TY0VwTU10L0dEVHM3V1k3RkpzK1R1K2NFeElmc0x0NnN5WEFwSU1nVHhvMElnc3NKZjhWVFc4eVlmSi9rRUo3T3V6d0k4cXZtK256VnpqVmJncGwiLCJtYWMiOiJmZTkyNzAxZjA2NTZlZjQ3ZWM3Zjg1MzFlNGUyMDRkNzYzM2IzY2VhY2MwNzk1ODJkMDZiY2Y1MDlhYzgzOTI1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InNPbUpwejFlaThFUUt0eDZuTWR5aVE9PSIsInZhbHVlIjoieGdyTHd3UXRnaUZnWUpBYXo2U0tIaEJQY0tPeXRocERuSUk2WjBOSEFHTk1oUExLUHJLSmJOMWJNUWVyNURKSVJBcDJTeUNPaGZkSGF2dkYveCtxdmg4azZHTitUcGFtcU5QLzFtVVBkT25sMDhaOHJvT1ZWWE5QQ3ozVGRBM0QiLCJtYWMiOiJhMjRjMmUwODE3YTUzYjA2NGE0MTc3ZTljZTQ3NTY3NWMyYzQyMTI5NjQzMzFlMzA3MWNmNDJmZjg5ZGVlMzQyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26545680\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1141338840 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:48:29 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141338840\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-146664648 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-146664648\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}