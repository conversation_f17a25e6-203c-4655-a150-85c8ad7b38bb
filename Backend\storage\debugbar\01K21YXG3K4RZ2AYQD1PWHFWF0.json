{"__meta": {"id": "01K21YXG3K4RZ2AYQD1PWHFWF0", "datetime": "2025-08-07 10:48:38", "utime": **********.89998, "method": "GET", "uri": "/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.715114, "end": **********.908737, "duration": 0.19362282752990723, "duration_str": "194ms", "measures": [{"label": "Booting", "start": **********.715114, "relative_start": 0, "end": **********.883046, "relative_end": **********.883046, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.883057, "relative_start": 0.*****************, "end": **********.908739, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "25.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.892524, "relative_start": 0.*****************, "end": **********.895633, "relative_end": **********.895633, "duration": 0.003108978271484375, "duration_str": "3.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.898507, "relative_start": 0.*****************, "end": **********.898617, "relative_end": **********.898617, "duration": 0.00010991096496582031, "duration_str": "110μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.898635, "relative_start": 0.*****************, "end": **********.898652, "relative_end": **********.898652, "duration": 1.71661376953125e-05, "duration_str": "17μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "194ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1636641603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1636641603\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-125325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-125325\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-602986453 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkZYVzFhdUMvR2lxb21IeHRoWEF4YlE9PSIsInZhbHVlIjoiYk1DeVhFQUhmdHUwOUtqbjc3V2I2M0VyZm16M243L0hYZldiQVI4cnc5WklvOGdLS3JmU3lmVGxkMUpkM3lvdzZIN1hRSUx0NVFiN3QwWTB4NlBGU083VU1pazZDbWRXNjhGc2xremJYM0ptbGNKSXRTVEVuRWhncEZTRzRIRisiLCJtYWMiOiJjMGVmODAzZDI5NmY2MzRkMTUyMmRmNDdlMDcyNGJkMjc3NGM2NmFkN2M5ZWFiMTE0YmI4MDJkODVlYjA4YTlhIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImZEcXVWQXNsOW1iN3FPN3BhNzdjVWc9PSIsInZhbHVlIjoiSHgvTDE4eUlPbGErNHNmN3NWTG1qand3bWNhU0NPMHpNMUJTTk4rVFVQYUNRRFhiSXIrd1dXRnJEeXhLbnhyMEhqZ3hnZFRKZW1OUk05aVk0MEZ0eEV2T0VWbkV2QWFHYTZ2MndhWjA0T1R6MEs2WE80VEZmS3ZQbVBKRXljSFEiLCJtYWMiOiI5NzkzOWFjMWQ2ODE2ZjczYTZjYTBmNTEwMGVkYTdlNDk0ZWE0MDNmZjQ3YTQ3OGE2ZmQ5MjVhM2U1NGE0NTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mlk.test/1022-iphone-ip16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602986453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-490701770 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZYVzFhdUMvR2lxb21IeHRoWEF4YlE9PSIsInZhbHVlIjoiYk1DeVhFQUhmdHUwOUtqbjc3V2I2M0VyZm16M243L0hYZldiQVI4cnc5WklvOGdLS3JmU3lmVGxkMUpkM3lvdzZIN1hRSUx0NVFiN3QwWTB4NlBGU083VU1pazZDbWRXNjhGc2xremJYM0ptbGNKSXRTVEVuRWhncEZTRzRIRisiLCJtYWMiOiJjMGVmODAzZDI5NmY2MzRkMTUyMmRmNDdlMDcyNGJkMjc3NGM2NmFkN2M5ZWFiMTE0YmI4MDJkODVlYjA4YTlhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImZEcXVWQXNsOW1iN3FPN3BhNzdjVWc9PSIsInZhbHVlIjoiSHgvTDE4eUlPbGErNHNmN3NWTG1qand3bWNhU0NPMHpNMUJTTk4rVFVQYUNRRFhiSXIrd1dXRnJEeXhLbnhyMEhqZ3hnZFRKZW1OUk05aVk0MEZ0eEV2T0VWbkV2QWFHYTZ2MndhWjA0T1R6MEs2WE80VEZmS3ZQbVBKRXljSFEiLCJtYWMiOiI5NzkzOWFjMWQ2ODE2ZjczYTZjYTBmNTEwMGVkYTdlNDk0ZWE0MDNmZjQ3YTQ3OGE2ZmQ5MjVhM2U1NGE0NTAzIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490701770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1025360232 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">32732</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">a4717e300cc331221e231ba8dbfdfa56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 09:48:38 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025360232\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1236294294 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236294294\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}