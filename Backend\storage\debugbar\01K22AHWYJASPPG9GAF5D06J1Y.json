{"__meta": {"id": "01K22AHWYJASPPG9GAF5D06J1Y", "datetime": "2025-08-07 14:12:01", "utime": **********.747321, "method": "GET", "uri": "/cache/large/category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 1, "messages": [{"message": "[14:12:01] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.73773, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.512836, "end": **********.756727, "duration": 0.24389100074768066, "duration_str": "244ms", "measures": [{"label": "Booting", "start": **********.512836, "relative_start": 0, "end": **********.688451, "relative_end": **********.688451, "duration": 0.****************, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.688465, "relative_start": 0.****************, "end": **********.756729, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "68.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.698879, "relative_start": 0.*****************, "end": **********.702395, "relative_end": **********.702395, "duration": 0.003515958786010742, "duration_str": "3.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.745532, "relative_start": 0.****************, "end": **********.745644, "relative_end": **********.745644, "duration": 0.00011205673217773438, "duration_str": "112μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.74566, "relative_start": 0.*****************, "end": **********.745672, "relative_end": **********.745672, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "244ms", "peak_memory": "38MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-439225948 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-439225948\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-202225127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-202225127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1721915891 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Ik1BSTU5NnMxc25BcU91MTJjMzJRR0E9PSIsInZhbHVlIjoiNU51dnMxdWVPUXUxalJWTjZRcTZXM1N4a203elBZYjFweEFNMS95WGE1STRtcytSTHl2dlVrMXNsUm0vNCtrR0w5aGVUZ2dWRWVPQ3RaUDJ2VzFHaXF3bHI0TXd6MjdFb1BsTXNxSC82bUxGMnZPQU5sYVdqK1pSbWdBRThrMVciLCJtYWMiOiJmYjk3Y2NjYWEyZjZjY2E2OTc4NGQxMDdkYmE5Mzk3OWM4MmNkZThmNzkxNWU5ZmRkMzBhOTZkMzZiODhlYWUzIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ikk4NEZqbjNPRkh1QWMxeS9CUy9ZWEE9PSIsInZhbHVlIjoiSVQyaFZialgyODc1bU9pUHlxZy84WWt6anIrUVZQaVIwUVV6SCt1dXpiK2ZlaDhac3JONHpPWG9zQU93ZE1nQTdEeVhGQitLbThIMkhxSTZIR21lRi9WQmlvQmJ1YTlkSlhEaE9wTFZzN2xGUjZRdzRoeGJkQWtiUURwcjRtQ3QiLCJtYWMiOiIwYWZiMzM1NWYxMmNlNGMxYjRmMTNiMzdjMTM4NGQ1ODk5NWI5YzA3NjEyMGVjMWRiMjE0NzllNTVmNDZjZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721915891\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1704317654 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1BSTU5NnMxc25BcU91MTJjMzJRR0E9PSIsInZhbHVlIjoiNU51dnMxdWVPUXUxalJWTjZRcTZXM1N4a203elBZYjFweEFNMS95WGE1STRtcytSTHl2dlVrMXNsUm0vNCtrR0w5aGVUZ2dWRWVPQ3RaUDJ2VzFHaXF3bHI0TXd6MjdFb1BsTXNxSC82bUxGMnZPQU5sYVdqK1pSbWdBRThrMVciLCJtYWMiOiJmYjk3Y2NjYWEyZjZjY2E2OTc4NGQxMDdkYmE5Mzk3OWM4MmNkZThmNzkxNWU5ZmRkMzBhOTZkMzZiODhlYWUzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikk4NEZqbjNPRkh1QWMxeS9CUy9ZWEE9PSIsInZhbHVlIjoiSVQyaFZialgyODc1bU9pUHlxZy84WWt6anIrUVZQaVIwUVV6SCt1dXpiK2ZlaDhac3JONHpPWG9zQU93ZE1nQTdEeVhGQitLbThIMkhxSTZIR21lRi9WQmlvQmJ1YTlkSlhEaE9wTFZzN2xGUjZRdzRoeGJkQWtiUURwcjRtQ3QiLCJtYWMiOiIwYWZiMzM1NWYxMmNlNGMxYjRmMTNiMzdjMTM4NGQ1ODk5NWI5YzA3NjEyMGVjMWRiMjE0NzllNTVmNDZjZjMxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704317654\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-928273484 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4500</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">15695d7338cabc1bc6b8d3e2c1157654</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 13:12:01 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928273484\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-738165435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-738165435\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/large/category/10/oSSwoZ0iLDYYlyCPTlT7f6prrIMYR5MXeVuh19Lb.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}